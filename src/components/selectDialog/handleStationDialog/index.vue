<template>
  <div>
    <el-dialog
      title="选择站点"
      :visible.sync="visible"
      :close-on-click-modal="false"
      @close="closeDialog"
      append-to-body
      width="80%"
    >
      <el-row :gutter="10">
        <!--部门数据-->
        <el-col :span="5" :xs="24">
          <div class="head-container">
            <el-tree
              :data="cityTreeList"
              :props="defaultProps"
              :expand-on-click-node="false"
              ref="tree"
              default-expand-all
              @node-click="handleNodeClick"
              style="height:70vh;overflow:auto"
            />
          </div>
        </el-col>
        <el-col :span="19" :xs="24">
          <AdvancedForm
            :config="config"
            :queryParams="searchForm"
            ref="AdvancedForm"
            showMore
            @confirm="handleQuery"
            @resetQuery="resetQuery"
            v-if="config.length"
          >
          </AdvancedForm>
          <el-card>
            <GridTable
              ref="gridTable"
              :columns="columns"
              :tableData="tableData"
              @radioChangeEvent="radioChangeEvent"
              :showRadio="true"
              :currentPage.sync="searchForm.pageNum"
              :pageSize.sync="searchForm.pageSize"
              :total.sync="tableTotal"
              @changePage="queryData"
              :loading="loading"
              :tableId="tableId"
              row-id="stationId"
              class="dialog-table"
            >
            </GridTable>
          </el-card>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button type="primary" @click.stop="handleConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import GridTable from "@/components/GridTable/index.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import {
  getCityTree,
  getStationList,
} from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
export default {
  components: {
    AdvancedForm,
    GridTable,
  },

  data() {
    return {
      visible: false,
      selectedObj: {},
      loading: false,
      tableId: "handleStationList",
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      cityTreeList: [],
      defaultProps: {
        children: "children",
        label: "areaName",
        id: "areaCode",
      },
      tableData: [],
      columns: [
        {
          field: "stationName",
          title: "站点名称",
          customWidth: 120,
        },
        {
          field: "stationAddress",
          title: "站点地址",
          customWidth: 100,
        },
        {
          field: "operationMode",
          title: "运营模式",
          formatter: ({ cellValue }) => {
            return (
              this.operationModeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "status",
          title: "运营状态",
          formatter: ({ cellValue }) => {
            return (
              this.statusOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "onlineDate",
          title: "上线日期",
        },
        {
          field: "deptName",
          title: "能投大区",
        },
      ],
      operationModeOptions: [],
      statusOptions: [],
    };
  },
  computed: {
    config() {
      return [
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "operationMode",
          title: "运营模式",
          type: "select",
          placeholder: "请选择运营模式",
          options: this.operationModeOptions,
        },
      ];
    },
  },
  watch: {},
  created() {
    this.getTreeSelect();
    Promise.all([
      this.getDicts("cm_operation_mode").then((response) => {
        this.operationModeOptions = response?.data;
      }),
      this.getDicts("cm_station_status").then((response) => {
        this.statusOptions = response?.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          // this.queryData();
        });
      }, 500);
    });
  },
  methods: {
    closeDialog() {
      this.visible = false;
    },
    open(row) {
      if (row) {
        this.selectedObj = JSON.parse(JSON.stringify(row));
      }
      this.visible = true;
      // this.resetQuery();
    },
    handleConfirm() {
      if (!this.selectedObj.stationId) {
        this.$message.warning("请选择站点");
        return false;
      }
      this.$emit("confirm", this.selectedObj);
      this.closeDialog();
    },
    /** 查询左侧下拉树结构 */
    getTreeSelect() {
      getCityTree({}).then((response) => {
        this.cityTreeList = response.data;
      });
    },
    radioChangeEvent(row) {
      console.log(row, "单选");
      this.selectedObj = { ...row };
    },
    // 节点单击事件
    handleNodeClick(data) {
      const { areaCode } = data;
      const len = areaCode.length;
      this.searchForm = {
        ...this.searchForm,
        provinceList: "",
        cityList: "",
        countyList: "",
      };
      len === 2 && (this.searchForm.provinceList = areaCode);
      len === 4 && (this.searchForm.cityList = areaCode);
      len === 6 && (this.searchForm.countyList = areaCode);
      this.queryData();
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      this.searchForm.pageSize = params.pageSize;
      this.queryData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取列表
    queryData() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      // this.selectedObj = {};
      // this.finallySearch = args;
      getStationList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
          this.$nextTick(() => {
            const table = this.$refs.gridTable?.$refs.xTable;
            table?.clearRadioRow();
            let selectRow = table?.getRowById(this.selectedObj?.stationId);
            if (selectRow) {
              table.setRadioRow(selectRow);
            }
          });
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-card__body {
  padding: 12px;
  padding-bottom: 8px;
}
/deep/.dialog-table .vxe-body--column {
  height: 32px !important;
}
/deep/ .vxe-grid--toolbar-wrapper {
  display: none;
}
/deep/ .el-dialog {
  margin-top: 2vh !important;
}
/deep/ .el-dialog__body {
  padding: 10px 20px;
}
/deep/ .pagination-container {
  margin-top: 0;
}
/deep/ .el-form-item--small.el-form-item {
  margin-bottom: 4px;
}
</style>
