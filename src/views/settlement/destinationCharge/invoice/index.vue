<!-- 发票 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowEdit="rowEdit"
      @rowDel="deleteRowHandler"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['destinationCharge:invoice:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['destinationCharge:invoice:import']"
          >导入</el-button
        >
        <el-button
          type="primary"
          @click="handleParseResult"
          v-has-permi="['destinationCharge:invoice:parseResult']"
          >发票解析结果</el-button
        >
      </template>

      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>

      <!-- 发票号码链接插槽 -->
      <template #invoiceId="{ row }">
        <el-link
          type="primary"
          @click="handleInvoiceIdClick(row)"
          :underline="false"
        >
          {{ row.invoiceId }}
        </el-link>
      </template>

      <!-- 户号链接插槽 -->
      <template #accountNumber="{ row }">
        <el-link
          type="primary"
          @click="handleAccountNumberClick(row.accountNumber)"
          :underline="false"
        >
          {{ row.accountNumber }}
        </el-link>
      </template>
      <template #billingPeriodList="{ params,item }">
        <BillingPeriodEditor v-model="params.billingPeriodList" />
      </template>
    </BuseCrud>
    <InvoiceUpload @uploadSuccess="loadData" ref="invoiceUpload">
    </InvoiceUpload>
    <!-- 文件预览组件 -->
    <PreviewFiles
      v-if="showFilePreview"
      :url-list="previewFileList"
      :initial-index="0"
      :file-options="{ url: 'filePath', name: 'fileName' }"
      :onClose="
        () => {
          showFilePreview = false;
        }
      "
    />
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/destinationCharge/invoice/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import InvoiceUpload from "./InvoiceUpload.vue";
import { queryCityTree } from "@/api/common.js";
import { queryLog } from "@/api/common.js";
import { getDeptList } from "@/api/operationWorkOrder/index.js";
import PreviewFiles from "@/components/PreviewFiles/index.vue";
import { fileDownLoad, getFileTypeFromUrl } from "@/utils/downLoad.js";
import { downLoadUrl2Blob } from "@/api/common.js";
import BillingPeriodEditor from "@/components/BillingPeriodEditor/index.vue";
export default {
  name: "destinationInvoice",
  components: { InvoiceUpload, PreviewFiles, BillingPeriodEditor },
  mixins: [exportMixin],
  data() {
    return {
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "accountId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "upload",
      //buse参数-e
      recordList: [],
      businessTypeOptions: [],
      onlineStatusOptions: [],
      serviceProviderOptions: [],
      institutionNameOptions: [],
      companyNameOptions: [],
      customerNameOptions: [],
      fileTypeOptions: [
        { dictLabel: "收入", dictValue: "income" },
        { dictLabel: "支出", dictValue: "expense" },
      ],

      // 文件预览相关状态
      showFilePreview: false,
      previewFileList: [],
      typeOptions: [],
      isEdit: false,
      regionData: [],

      // 字典数据
      operationStatusOptions: [], // 运营状态字典
      stationChargeTypeOptions: [], // 站点充电类型字典
      settlementCycleOptions: [], // 结算周期字典
      settlementTypeOptions: [], // 结算类型字典
      reportFormOptions: [], // 报电形式字典
      deptOptionList: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取运营状态字典
    this.getDicts("cm_station_status").then((response) => {
      this.operationStatusOptions = response.data;
    });

    // 获取站点充电类型字典
    this.getDicts("cm_station_charge_type").then((response) => {
      this.stationChargeTypeOptions = response.data;
    });

    // 获取结算周期字典
    this.getDicts("settlement_cycle").then((response) => {
      this.settlementCycleOptions = response.data;
    });

    // 获取结算类型字典
    this.getDicts("settlement_type").then((response) => {
      this.settlementTypeOptions = response.data;
    });

    // 获取报电形式字典
    this.getDicts("report_form").then((response) => {
      this.reportFormOptions = response.data;
    });
    this.getCityRegionData();
    this.getDeptList();
    // 获取下拉列表数据
    // api.getDropLists().then((res) => {
    //   if (res.success) {
    //     // 处理下拉列表数据
    //     if (res.data.institutionName) {
    //       this.institutionNameOptions = res.data.institutionName.map(
    //         (item) => ({
    //           dictLabel: item,
    //           dictValue: item,
    //         })
    //       );
    //     }
    //     if (res.data.serviceProvider) {
    //       this.serviceProviderOptions = res.data.serviceProvider.map(
    //         (item) => ({
    //           dictLabel: item,
    //           dictValue: item,
    //         })
    //       );
    //     }
    //   }
    // });

    // this.loadData();
  },
  activated() {
    if (this.$route.params.invoiceRecordId) {
      this.params = {
        ...initParams(this.filterOptions.config),
        invoiceRecordId: this.$route.params.invoiceRecordId,
      };
    }
    this.loadData();
  },
  methods: {
    checkPermission,
    //能投大区下拉选项
    getDeptList() {
      getDeptList({}).then((res) => {
        this.deptOptionList = res.data.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    handleClose() {
      this.$refs.crud.switchModalView(false);
    },

    handleBatchImport() {
      this.$refs.invoiceUpload.open();
    },

    // 处理发票解析结果按钮点击
    handleParseResult() {
      this.$router.push({
        name: "invoiceParseResult",
      });
    },

    // 处理发票号码点击事件
    handleInvoiceIdClick(row) {
      // 跳转到发票解析结果页面，并传递invoiceRecordId参数
      this.$router.push({
        name: "invoiceParseResult",
        params: {
          invoiceRecordId: row.invoiceRecordId,
        },
      });
    },
    handleAdd() {
      this.isEdit = false;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.isEdit = true;
      this.operationType = "update";

      // 处理账单周期数据
      let editData = { ...row };
      if (editData.billingPeriodList) {
        try {
          // 如果是字符串，尝试解析为数组
          if (typeof editData.billingPeriodList === "string") {
            editData.billingPeriodList = JSON.parse(editData.billingPeriodList);
          }
          // 如果是数组，直接使用
          if (!Array.isArray(editData.billingPeriodList)) {
            editData.billingPeriodList = [];
          }
        } catch (error) {
          console.error("解析账单周期数据失败:", error);
          editData.billingPeriodList = [];
        }
      } else {
        editData.billingPeriodList = [];
      }

      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...editData,
      });
    },

    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.exportData, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "billingDate",
          title: "账单周期",
          startFieldName: "billingStartDate",
          endFieldName: "billingEndDate",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.queryList(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      return new Promise(async (resolve) => {
        let params = { ...formParams };

        // 处理账单周期数据，转换为JSON字符串
        if (
          params.billingPeriodList &&
          Array.isArray(params.billingPeriodList)
        ) {
          params.billingPeriodList = JSON.stringify(params.billingPeriodList);
        }

        console.log(crudOperationType, formParams, "提交");
        // crudOperationType:update
        const res = await api.update(params);
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          id: row.id,
          invoiceId: row.invoiceId,
        };
        api.remove(params).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("删除成功");
            this.loadData();
          }
        });
      });
    },

    handleLog(row) {
      queryLog({ businessId: row.accountId }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },

    // 获取站点名称列表的方法
    async fetchStationNameList(queryString) {
      try {
        const res = await api.getStationNameList({ name: queryString });
        return res.data?.map((name) => ({ value: name })) || [];
      } catch (error) {
        console.error("获取站点名称列表失败:", error);
        return [];
      }
    },

    // 站点编号选择变化处理
    handleStationCodeChange({ value, options }) {
      const selectedStation = options.find(
        (item) => item.stationCode === value
      );
      if (selectedStation) {
        // 自动填充站点名称和基础信息
        this.$refs.crud.setFormFields({
          ...selectedStation,
        });
      }
    },

    // 站点名称选择变化处理
    handleStationNameChange({ value, options }) {
      const selectedStation = options.find(
        (item) => item.stationName === value
      );
      if (selectedStation) {
        // 自动填充站点编号和基础信息
        this.$refs.crud.setFormFields({
          ...selectedStation,
        });
      }
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = this.cleanTree(res.data);
      });
    },
    cleanTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },
    querySearch(queryString, cb, fieldName) {
      api
        .queryDistinctValue({
          fieldName: fieldName,
          fieldValue: queryString,
          pageNum: 1,
          pageSize: 9999,
        })
        .then((res) => {
          const result = res.data?.map((x) => {
            return { value: x };
          });
          cb(result);
        });
    },
    handlePreview(row) {
      if (row.invoiceFileUrl) {
        const type = getFileTypeFromUrl(row.invoiceFileUrl);
        console.log("type", type);
        this.previewFileList = [
          {
            filePath: row.invoiceFileUrl,
            fileName: `${row.accountNumber || ""}_${row.invoiceType ||
              ""}_${row.issueDate || ""}.${type}`,
          },
        ];
        this.showFilePreview = true;
      } else {
        this.$message.warning("暂无发票文件");
      }
    },

    // 处理户号点击事件
    handleAccountNumberClick(accountNumber) {
      // 跳转到户号列表页，并传递户号参数进行搜索
      this.$router.push({
        name: "destinationAccountNo",
        params: {
          accountNo: accountNumber,
        },
      });
    },
    async fetchInvoiceSuggestions(queryString, cb) {
      try {
        const res = await api.getInvoiceTypeList({ name: queryString });
        const suggestions = res.data?.map((type) => ({ value: type })) || [];
        cb(suggestions);
      } catch (error) {
        console.error("获取发票类型失败:", error);
        cb([]);
      }
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "invoiceId",
          title: "发票号码",
          width: 200,
          slots: { default: "invoiceId" },
        },
        {
          field: "invoiceType",
          title: "发票类型",
          width: 180,
        },
        {
          field: "accountNumber",
          title: "户号",
          width: 140,
          slots: { default: "accountNumber" },
        },
        {
          field: "stationName",
          title: "站点名称",
          width: 120,
        },
        {
          field: "issueDate",
          title: "开票日期",
          width: 140,
        },
        {
          field: "buyerName",
          title: "购买方名称",
          width: 150,
        },
        {
          field: "sellerName",
          title: "销售方名称",
          width: 150,
        },
        {
          field: "invoiceAmount",
          title: "发票金额(元)",
          width: 120,
        },
        {
          field: "electricityConsumption",
          title: "电量(kWh)",
          width: 120,
        },
        {
          field: "electricityFee",
          title: "电费(元)",
          width: 120,
        },
        {
          field: "billingPeriod",
          title: "账单周期",
          width: 150,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 150,
        },
        {
          field: "createUserName",
          title: "创建人",
          width: 150,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "accountNumber",
            element: "el-input",
            title: "户号",
          },
          {
            field: "billingDate",
            title: "账单周期",
            element: "el-date-picker",
            props: {
              type: "monthrange",
              valueFormat: "yyyy-MM",
            },
          },
          {
            field: "stationName",
            element: "el-autocomplete",
            title: "站点名称",
            props: {
              clearable: true,
              fetchSuggestions: (queryString, cb) => {
                this.fetchStationNameList(queryString).then(cb);
              },
              placeholder: "请输入站点名称",
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const formConfig = [
        {
          field: "invoiceId",
          title: "发票号码",
          element: "el-input",
          rules: [{ required: true, message: "请输入发票号码" }],
          attrs: {
            placeholder: "请输入发票号码",
          },
        },
        {
          field: "invoiceType",
          title: "发票类型",
          element: "el-autocomplete",
          rules: [{ required: false, message: "请输入发票类型" }],
          props: {
            fetchSuggestions: this.fetchInvoiceSuggestions,
            placeholder: "请输入发票类型",
          },
        },
        {
          field: "issueDate",
          title: "开票日期",
          element: "el-date-picker",
          props: {
            type: "date",
            valueFormat: "yyyy-MM-dd",
            placeholder: "选择开票日期",
          },
        },
        {
          field: "buyerName",
          title: "购买方名称",
          element: "el-input",
          attrs: {
            placeholder: "请输入购买方名称",
          },
        },
        {
          field: "buyerTaxId",
          title: "购买方纳税识别号",
          element: "el-input",
          attrs: {
            placeholder: "请输入购买方纳税识别号",
          },
        },
        {
          field: "sellerName",
          title: "销售方名称",
          element: "el-input",
          attrs: {
            placeholder: "请输入销售方名称",
          },
        },
        {
          field: "sellerTaxId",
          title: "销售方纳税识别号",
          element: "el-input",
          attrs: {
            placeholder: "请输入销售方纳税识别号",
          },
        },
        {
          field: "invoiceAmount",
          title: "发票金额(元)",
          element: "el-input",
          attrs: {
            placeholder: "请输入发票金额",
          },
        },
        {
          field: "accountNumber",
          title: "户号",
          element: "el-input",
          attrs: {
            placeholder: "请输入户号",
          },
        },
        {
          field: "electricityConsumption",
          title: "电量(kWh)",
          element: "el-input",
          attrs: {
            placeholder: "请输入电量",
          },
        },
        {
          field: "electricityFee",
          title: "电费(元)",
          element: "el-input",
          attrs: {
            placeholder: "请输入电费",
          },
        },
        {
          field: "billingPeriodList",
          title: "账单周期",
          element: "slot",
          slotName: "billingPeriodList",
          defaultValue: [],
        },
      ];
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["destinationCharge:invoice:edit"]),
        delBtn: checkPermission(["destinationCharge:invoice:delete"]),
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "80%",
        formConfig: formConfig,
        crudPermission: [],
        customOperationTypes: [
          {
            title: "查看发票",
            typeName: "preview",
            slotName: "preview",
            showForm: false,
            event: (row) => {
              return this.handlePreview(row);
            },
            condition: () => {
              return checkPermission(["destinationCharge:invoice:preview"]);
            },
          },
          {
            title: "下载发票",
            typeName: "download",
            slotName: "download",
            showForm: false,
            event: (row) => {
              return new Promise((resolve) => {
                // 构建文件名：户号+发票类型+开票日期
                const type = getFileTypeFromUrl(row.invoiceFileUrl);
                const fileName = `${row.accountNumber ||
                  ""}_${row.invoiceType || ""}_${row.issueDate || ""}.${type}`;

                downLoadUrl2Blob({
                  fileUrl: row.invoiceFileUrl,
                })
                  .then(async (res) => {
                    if (res) {
                      await fileDownLoad(res, fileName);
                    }
                  })
                  .finally(() => {
                    resolve();
                  });
              });
            },
            condition: () => {
              return checkPermission(["destinationCharge:invoice:download"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "200px",
        },
      };
    },
  },
};
</script>

<style></style>
