{"openapi": "3.0.1", "info": {"title": "商服维保通项目接口管理", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/st/invoice/record/queryList": {"post": {"summary": "分页查询", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StInvoiceRecordQueryDTO", "description": ""}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResultStInvoiceRecord"}, "example": {"success": false, "code": "", "message": "", "data": [{"id": 0, "result": "", "startTime": "", "endTime": "", "createBy": 0, "createName": "", "status": ""}], "traceId": "", "pageNum": 0, "pageSize": 0, "total": 0}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {"StInvoiceRecord": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id", "format": "int64"}, "result": {"type": "string", "description": "解析结果"}, "startTime": {"type": "string", "description": "开始解析时间"}, "endTime": {"type": "string", "description": "解析完成时间"}, "createBy": {"type": "integer", "description": "创建人", "format": "int64"}, "createName": {"type": "string", "description": "创建人名称"}, "status": {"type": "string", "description": "解析状态(01 成功  02 失败 03 解析中)"}}}, "PaginationResultStInvoiceRecord": {"type": "object", "properties": {"success": {"type": "boolean", "description": ""}, "code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/StInvoiceRecord", "description": "发票解析记录表"}, "description": ""}, "traceId": {"type": "string", "description": ""}, "pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "total": {"type": "integer", "description": ""}}}, "StInvoiceRecordQueryDTO": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "tenantId": {"type": "integer", "description": "", "format": "int64"}, "orgNo": {"type": "integer", "description": "", "format": "int64"}, "orgNoList": {"type": "array", "items": {"type": "integer"}, "description": ""}, "operatorId": {"type": "integer", "description": "", "format": "int64"}, "operatorName": {"type": "string", "description": ""}, "invoiceRecordId": {"type": "integer", "description": "", "format": "int64"}}}}, "securitySchemes": {}}, "servers": [], "security": []}