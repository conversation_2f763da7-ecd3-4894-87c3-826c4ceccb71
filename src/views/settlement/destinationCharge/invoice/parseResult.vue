<!-- 发票解析结果 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @loadData="loadData"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>

      <!-- 解析状态插槽 -->
      <template #status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ formatParseStatus(row.status) }}
        </el-tag>
      </template>
      <template #endTime="{ row }">
        <el-link @click="handleJumpBack(row)" :disabled="row.status !== '01'">{{
          row.endTime
        }}</el-link>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import api from "@/api/settlement/destinationCharge/invoice/index.js";
import { initParams } from "@/utils/buse.js";

export default {
  name: "invoiceParseResult",
  data() {
    return {
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      // 解析状态字典
      parseStatusOptions: [
        { dictLabel: "成功", dictValue: "01" },
        { dictLabel: "失败", dictValue: "02" },
        { dictLabel: "解析中", dictValue: "03" },
      ],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    console.log(this.$route);
    // 检查是否有传递的invoiceRecordId参数
    if (this.$route.params.invoiceRecordId) {
      this.params.invoiceRecordId = this.$route.params.invoiceRecordId;
    }
    this.loadData();
  },

  methods: {
    handleJumpBack(row) {
      this.$router.push({
        name: "destinationInvoice",
        params: {
          invoiceRecordId: row.id,
        },
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.loading = true;
      const res = await api.queryInvoiceRecordList(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);

      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    // 格式化解析状态显示
    formatParseStatus(status) {
      const statusMap = {
        "01": "成功",
        "02": "失败",
        "03": "解析中",
      };
      return statusMap[status] || status;
    },

    // 格式化解析状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        "01": "success",
        "02": "danger",
        "03": "warning",
      };
      return typeMap[status] || "";
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "startTime",
          title: "开始解析时间",
        },
        {
          field: "endTime",
          title: "解析完成时间",
          slots: {
            default: "endTime",
          },
        },
        {
          field: "createName",
          title: "创建人名称",
        },
        {
          field: "status",
          title: "解析状态",
          slots: { default: "status" },
        },
        {
          field: "result",
          title: "解析结果",
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "status",
            element: "el-select",
            title: "解析状态",
            props: {
              options: this.parseStatusOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        viewBtn: false,
        submitBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        formConfig: [],
      };
    },
  },
};
</script>

<style></style>
