<template>
  <div>
    <el-dialog
      title="选择设备"
      :visible.sync="visible"
      :close-on-click-modal="false"
      @close="closeDialog"
      append-to-body
      width="80%"
    >
      <AdvancedForm
        :config="config"
        :queryParams="searchForm"
        ref="AdvancedForm"
        showMore
        @confirm="handleQuery"
        @resetQuery="resetQuery"
        v-if="config.length"
      >
      </AdvancedForm>
      <el-card>
        <GridTable
          ref="gridTable"
          :columns="columns"
          :tableData="tableData"
          :checkbox="true"
          :batchDelete="true"
          @handleSelectionChange="tableSelect"
          :currentPage.sync="searchForm.pageNum"
          :pageSize.sync="searchForm.pageSize"
          :total.sync="tableTotal"
          @changePage="queryData"
          :loading="loading"
          :tableId="tableId"
          row-id="deviceNo"
          class="dialog-table"
        >
        </GridTable>
      </el-card>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button type="primary" @click.stop="handleConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import GridTable from "@/components/GridTable/index.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import { getDeviceList } from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
export default {
  components: {
    AdvancedForm,
    GridTable,
  },
  props: {
    orderNo: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      stationCode: "",
      selectedList: [],
      visible: false,
      selectedObj: {},
      loading: false,
      tableId: "handleStationList",
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      subTypeOptions: [],
      tableData: [],
      columns: [
        { type: "checkbox", customWidth: 60 },
        {
          field: "deviceNo",
          title: "设备编码",
        },
        {
          field: "deviceName",
          title: "设备名称",
          customWidth: 100,
        },
        {
          field: "subType",
          title: "设备类型",
          formatter: ({ cellValue }) => {
            return (
              this.subTypeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "stationName",
          title: "所属站点",
        },
        {
          field: "provinceInfo",
          title: "省市区",
        },
      ],
    };
  },
  computed: {
    config() {
      return [
        {
          key: "deviceNo",
          title: "设备编码",
          type: "input",
          placeholder: "请输入设备编码",
        },
        {
          key: "deviceName",
          title: "设备名称",
          type: "input",
          placeholder: "请输入设备名称",
        },
        // {
        //   key: "stationName",
        //   title: "站点名称",
        //   type: "input",
        //   placeholder: "请输入站点名称",
        // },
        {
          key: "subTypeCode",
          title: "设备类型",
          type: "select",
          options: this.subTypeOptions,
          placeholder: "请输入设备类型",
        },
      ];
    },
  },
  watch: {},
  created() {
    Promise.all([
      this.getDicts("cm_sub_type").then((response) => {
        this.subTypeOptions = response?.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          // this.queryData();
        });
      }, 500);
    });
  },
  methods: {
    closeDialog() {
      this.$refs.gridTable.clearTips();
      this.visible = false;
    },
    open(stationCode) {
      this.visible = true;
      this.stationCode = stationCode;
      // this.resetQuery();
    },
    handleConfirm() {
      if (!this.selectedList?.length > 0) {
        this.$message.warning("请选择至少一条数据");
        return false;
      }
      this.$emit("confirm", this.selectedList);
      //   this.closeDialog();
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.selectedList = tableData;
    },

    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      this.searchForm.pageSize = params.pageSize;
      this.queryData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取列表
    queryData() {
      this.loading = true;
      let params = {
        ...this.searchForm,
        orderNo: this.orderNo,
        stationCode: this.stationCode,
      };
      this.selectedObj = {};
      // this.finallySearch = args;
      getDeviceList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-card__body {
  padding: 12px;
  padding-bottom: 8px;
}
/deep/.dialog-table .vxe-body--column {
  height: 32px !important;
}
/deep/ .vxe-grid--toolbar-wrapper {
  display: none;
}
/deep/ .el-dialog {
  margin-top: 2vh !important;
}
/deep/ .el-dialog__body {
  padding: 10px 20px;
}
/deep/ .pagination-container {
  margin-top: 0;
}
/deep/ .el-form-item--small.el-form-item {
  margin-bottom: 4px;
}
</style>
