//结果查询
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
      selectStyleType="infoTip"
      @handleBatchSelect="handleBatchSelect"
      :showSelectNum="showSelectNum"
    >
      <template #toolbar_buttons>
        <el-select
          v-model="selectPage"
          size="mini"
          style="margin-right: 10px; width: 86px;"
        >
          <el-option label="当前页" value="1"></el-option>
          <el-option label="全部页" value="2"></el-option>
        </el-select>
        <el-button
          type="primary"
          @click="handleBatchLift"
          v-has-permi="['ruleEngine:resultSearch:batchLift']"
          >批量解除</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['ruleEngine:resultSearch:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>

      <template #alarmContent="{ row }">
        <el-link type="primary" @click="handleViewDetail(row)">
          {{ row.businessTypeName }}
        </el-link>
      </template>
      <template #statusChange="{ row }"></template>
      <template slot="log" slot-scope="{ row, operationType }">
        <el-tabs v-model="activeLogTab" type="card">
          <el-tab-pane label="处理记录" name="process">
            <Timeline
              :list="recordList"
              operateTypeTitle="operateType"
              operatorNameTitle="operatorName"
              createTimeTitle="createTime"
              operateDetailTitle="operateDetail"
            />
          </el-tab-pane>
          <el-tab-pane label="执行日志" name="execution">
            <ExecutionLogTable
              :alarmId="row.alarmId"
              :tableHeight="400"
              :showPagination="false"
            />
          </el-tab-pane>
        </el-tabs>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ruleEngine/resultSearch.js";
import Timeline from "@/components/Timeline/index.vue";
import ExecutionLogTable from "./components/ExecutionLogTable.vue";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";

export default {
  name: "resultSearchPage",
  components: { Timeline, ExecutionLogTable },
  mixins: [exportMixin],
  data() {
    return {
      selectedData: [],
      selectPage: "1", // 当前页/全部页选择
      isBatch: false,
      statusOptions: [],
      riskLevelOptions: [],
      recordList: [],
      activeLogTab: "process", // 日志弹窗默认tab
      businessTypeOptions: [],
      //buse参数-s

      tableData: [],
      tableColumn: [
        {
          type: "checkbox",
          width: 60,
          fixed: "left",
        },
        {
          field: "supportDept",
          title: "告警编码",
          width: 150,
        },
        {
          field: "urgencyDefinition",
          title: "规则名称",
          width: 150,
        },
        {
          field: "urgencyDefinition",
          title: "触发值/阈值",
          width: 150,
        },
        {
          field: "urgencyName",
          title: "风险等级",
          width: 150,
        },
        {
          field: "urgencyDefinition",
          title: "告警时间",
          width: 150,
        },
        {
          field: "status",
          title: "状态",
          width: 150,
          slots: {
            default: "statusChange",
          },
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      deptOptions: [],
    };
  },
  watch: {
    // 监听页面选择器变化
    selectPage(newVal) {
      if (newVal === "2") {
        // 选择全部页时，清空列表已勾选
        this.clearSelectedData();
      }
    },
  },
  computed: {
    tableProps() {
      const that = this;
      return {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
          // 添加checkMethod方法，用于根据条件禁用复选框
          checkMethod({ row }) {
            // 全部页模式时禁用勾选框
            if (that.selectPage === "2") {
              return false;
            }
          },
        },
      };
    },
    showSelectNum() {
      return this.selectedData?.length > 0;
    },
    // 批量操作按钮是否禁用
    isBatchOperationDisabled() {
      return this.selectPage === "1" && this.selectedData.length === 0;
    },
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "supportDept",
            title: "规则名称",
          },
          {
            field: "status",
            element: "el-select",
            title: "状态",
            props: {
              options: this.statusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "riskLevel",
            element: "el-select",
            title: "风险等级",
            props: {
              options: this.riskLevelOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
            },
          },
          {
            field: "warnTime",
            element: "el-date-picker",
            title: "告警时间",
            props: {
              valueFormat: "yyyy-MM-dd",
              type: "daterange",
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增紧急程度",
        editBtn: false,
        editTitle: "编辑紧急程度",
        delBtn: false,
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "count",
            title: "符合解除条件的条数",
            preview: true,
            show: this.isBatch,
          },
          {
            field: "status",
            title: "解除进度",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.statusOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择解除进度",
              },
            ],
          },
          {
            field: "urgencyDefinition",
            title: "解除原因",
            props: {
              type: "textarea",
            },
            attrs: {
              placeholder: "500个字符以内",
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
            },
          },
        ],
        customOperationTypes: [
          {
            title: "解除",
            modalTitle: "解除预警",
            typeName: "lift",
            event: (row) => {
              return this.handleLift(row);
            },
            condition: (row) => {
              return checkPermission(["ruleEngine:resultSearch:lift"]);
            },
          },
          {
            title: "日志",
            modalTitle: "操作日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["ruleEngine:resultSearch:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "180px",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  mounted() {
    this.getDicts("support_dept").then((response) => {
      this.deptOptions = response.data;
    });
    this.loadData();
  },
  methods: {
    handleBatchSelect(arr) {
      console.log(arr, "已选择");
      this.selectedData = arr;
    },
    /** 清空选中的数据 */
    clearSelectedData() {
      if (this.$refs.crud) {
        this.$refs.crud.tableDeselectHandler();
      }
      this.selectedData = [];
    },
    handleBatchLift() {
      if (this.selectPage === "1" && this.selectedData.length === 0) {
        this.$message.warning("请至少勾选一条数据");
        return;
      }
      this.isBatch = true;
      this.$refs.crud.switchModalView(true, "lift", {
        ...initParams(this.modalConfig.formConfig),
        ids: this.selectedData,
      });
    },
    handleLift(row) {
      this.isBatch = false;
      this.$refs.crud.switchModalView(true, "lift", {
        ...initParams(this.modalConfig.formConfig),
        id: row.id,
      });
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,
    handleLog(row) {
      api.queryLog({ urgencyId: row.urgencyId }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },

    handleExport() {
      const params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);

      this.handleCommonExport(api.export, params);
    },
    handleTimeRange(params) {
      const arr = [
        {
          field: "warnTime",
          title: "告警时间",
          startFieldName: "submitStartTime",
          endFieldName: "submitEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");

      api.update(formParams).then((res) => {
        if (res.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
        }
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          ...row,
        };
        api.deleteData(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {
      this.$refs.crud.switchModalView(true, "ADD");
    },
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...row,
        businessTypeIds: row.businessType?.split(",") || [],
      });
    },
    // 查看告警详情
    handleViewDetail(row) {
      this.$router.push({
        path: "/rule-engine/resultSearch/detail",
        query: {
          id: row.urgencyId || row.id,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
</style>
