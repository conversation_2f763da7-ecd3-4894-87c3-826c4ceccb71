//结果查询
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
      selectStyleType="infoTip"
      @handleBatchSelect="handleBatchSelect"
      :showSelectNum="showSelectNum"
    >
      <template #toolbar_buttons>
        <el-select
          v-model="selectPage"
          size="mini"
          style="margin-right: 10px; width: 86px;"
        >
          <el-option label="当前页" value="1"></el-option>
          <el-option label="全部页" value="2"></el-option>
        </el-select>
        <el-button
          type="primary"
          @click="handleBatchLift"
          v-has-permi="['ruleEngine:resultSearch:batchLift']"
          >批量解除</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['ruleEngine:resultSearch:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>

      <!-- 告警内容列 -->
      <template #alarmContent="{ row }">
        <el-link type="primary" @click="handleViewDetail(row)">
          {{ row.nodeName }}
        </el-link>
      </template>

      <!-- 规则名称列 -->
      <template #ruleName="{ row }">
        <el-link type="primary" @click="handleRuleNameClick(row)">
          {{ row.ruleName }}
        </el-link>
      </template>

      <!-- 风险等级列 -->
      <template #riskLevel="{ row }">
        <span :class="getRiskLevelClass(row.riskLevel)">
          {{ getRiskLevelName(row.riskLevel) }}
        </span>
      </template>

      <!-- 状态列 -->
      <template #alarmStatus="{ row }">
        <el-tag :type="getStatusTagType(row.alarmStatus)" size="small">
          {{ getStatusName(row.alarmStatus) }}
        </el-tag>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <el-tabs v-model="activeLogTab" type="card">
          <el-tab-pane label="处理记录" name="process">
            <Timeline
              :list="recordList"
              operateTypeTitle="operateType"
              operatorNameTitle="operatorName"
              createTimeTitle="createTime"
              operateDetailTitle="operateDetail"
            />
          </el-tab-pane>
          <el-tab-pane label="执行日志" name="execution">
            <ExecutionLogTable
              :alarmId="row.alarmId"
              :tableHeight="400"
              :showPagination="false"
            />
          </el-tab-pane>
        </el-tabs>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ruleEngine/resultSearch.js";
import { queryLog } from "@/api/common.js";
import Timeline from "@/components/Timeline/index.vue";
import ExecutionLogTable from "./components/ExecutionLogTable.vue";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";

export default {
  name: "resultSearchPage",
  components: { Timeline, ExecutionLogTable },
  mixins: [exportMixin],
  data() {
    return {
      selectedData: [],
      selectPage: "1", // 当前页/全部页选择
      isBatch: false,
      statusOptions: [],
      riskLevelOptions: [],
      recordList: [],
      activeLogTab: "process", // 日志弹窗默认tab
      businessTypeOptions: [],
      //buse参数-s

      tableData: [],
      tableColumn: [
        {
          type: "checkbox",
          width: 60,
          fixed: "left",
        },
        {
          field: "alarmCode",
          title: "告警编码",
          width: 150,
        },
        {
          field: "nodeName",
          title: "告警内容",
          width: 150,
          slots: {
            default: "alarmContent",
          },
        },
        {
          field: "ruleName",
          title: "规则名称",
          width: 150,
          slots: {
            default: "ruleName",
          },
        },
        {
          field: "triggerValueAndThreshold",
          title: "触发值/阈值",
          width: 150,
        },
        {
          field: "riskLevel",
          title: "风险等级",
          width: 150,
          slots: {
            default: "riskLevel",
          },
        },
        {
          field: "alarmTime",
          title: "告警时间",
          width: 150,
        },
        {
          field: "alarmStatus",
          title: "状态",
          width: 150,
          slots: {
            default: "alarmStatus",
          },
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      deptOptions: [],
    };
  },
  watch: {
    // 监听页面选择器变化
    selectPage(newVal) {
      if (newVal === "2") {
        // 选择全部页时，清空列表已勾选
        this.clearSelectedData();
      }
    },
  },
  computed: {
    tableProps() {
      const that = this;
      return {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
          // 添加checkMethod方法，用于根据条件禁用复选框
          checkMethod({ row }) {
            // 全部页模式时禁用勾选框
            if (that.selectPage === "2") {
              return false;
            }
            // 只允许勾选状态为"未解除"(0)或"处理中"(1)的数据行
            return row.alarmStatus === "0" || row.alarmStatus === "1";
          },
        },
      };
    },
    showSelectNum() {
      return this.selectedData?.length > 0;
    },
    // 批量操作按钮是否禁用
    isBatchOperationDisabled() {
      return this.selectPage === "1" && this.selectedData.length === 0;
    },
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "ruleName",
            title: "规则名称",
          },
          {
            field: "alarmStatus",
            element: "el-select",
            title: "状态",
            props: {
              options: this.statusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "riskLevel",
            element: "el-select",
            title: "风险等级",
            props: {
              options: this.riskLevelOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
            },
          },
          {
            field: "alarmTime",
            element: "el-date-picker",
            title: "告警时间",
            props: {
              valueFormat: "yyyy-MM-dd",
              type: "daterange",
              startPlaceholder: "开始日期",
              endPlaceholder: "结束日期",
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增紧急程度",
        editBtn: false,
        editTitle: "编辑紧急程度",
        delBtn: false,
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "count",
            title: "符合解除条件的条数",
            preview: true,
            show: this.isBatch,
          },
          {
            field: "status",
            title: "解除进度",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.statusOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择解除进度",
              },
            ],
          },
          {
            field: "urgencyDefinition",
            title: "解除原因",
            props: {
              type: "textarea",
            },
            attrs: {
              placeholder: "500个字符以内",
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
            },
          },
        ],
        customOperationTypes: [
          {
            title: "解除",
            modalTitle: "解除预警",
            typeName: "lift",
            event: (row) => {
              return this.handleLift(row);
            },
            condition: (row) => {
              // 仅在状态为"未解除"(0)或"处理中"(1)时显示解除按钮
              const canRelieve =
                row.alarmStatus === "0" || row.alarmStatus === "1";
              return (
                canRelieve && checkPermission(["ruleEngine:resultSearch:lift"])
              );
            },
          },
          {
            title: "日志",
            modalTitle: "操作日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["ruleEngine:resultSearch:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "180px",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  mounted() {
    // 获取告警处理状态字典
    this.getDicts("alarm_handle_status").then((response) => {
      this.statusOptions = response.data;
    });

    // 获取风险等级字典
    this.getDicts("alarm_risk_level").then((response) => {
      this.riskLevelOptions = response.data;
    });

    // 获取部门字典（保留原有逻辑）
    this.getDicts("support_dept").then((response) => {
      this.deptOptions = response.data;
    });

    this.loadData();
  },
  methods: {
    handleBatchSelect(arr) {
      console.log(arr, "已选择");
      this.selectedData = arr;
    },
    /** 清空选中的数据 */
    clearSelectedData() {
      if (this.$refs.crud) {
        this.$refs.crud.tableDeselectHandler();
      }
      this.selectedData = [];
    },
    async handleBatchLift() {
      if (this.selectPage === "1" && this.selectedData.length === 0) {
        this.$message.warning("请至少勾选一条数据");
        return;
      }

      this.isBatch = true;
      let count = 0;

      if (this.selectPage === "1") {
        // 选择当前页：统计已勾选的符合解除条件的行数
        count = this.selectedData.filter(
          (item) => item.alarmStatus === "0" || item.alarmStatus === "1"
        ).length;
      } else {
        // 选择全部页：调用接口获取总符合条件数量
        try {
          const res = await api.getDismissibleCount();
          count = res.data || 0;
        } catch (error) {
          this.$message.error("获取符合解除条件的数量失败");
          console.error(error);
          return;
        }
      }

      this.$refs.crud.switchModalView(true, "lift", {
        ...initParams(this.modalConfig.formConfig),
        count: count,
        ids: this.selectPage === "1" ? this.selectedData : [],
        allPageFlag: this.selectPage === "2",
      });
    },
    handleLift(row) {
      this.isBatch = false;
      this.$refs.crud.switchModalView(true, "lift", {
        ...initParams(this.modalConfig.formConfig),
        id: row.id,
      });
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,
    handleLog(row) {
      // 使用common.js的queryLog接口获取处理记录
      queryLog({ businessId: row.alarmCode }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },

    handleExport() {
      const params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);

      this.handleCommonExport(api.export, params);
    },
    handleTimeRange(params) {
      const arr = [
        {
          field: "alarmTime",
          title: "告警时间",
          startFieldName: "alarmStartTime",
          endFieldName: "alarmEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");

      if (crudOperationType === "lift") {
        // 解除预警
        const params = {
          ...this.params, // 当前查询条件
          relieveReason: formParams.urgencyDefinition,
          alarmStatus: formParams.status,
          allPageFlag: formParams.allPageFlag || false,
        };

        if (formParams.ids && formParams.ids.length > 0) {
          // 当前页选择的数据
          params.alarmCodeList = formParams.ids.map((item) => item.alarmCode);
        }

        api.relieve(params).then((res) => {
          if (res.code === "10000" || res.success) {
            this.$message.success("解除成功");
            this.loadData();
            this.clearSelectedData();
          }
        });
      } else {
        // 其他操作保持原有逻辑
        api.update(formParams).then((res) => {
          if (res.code === "10000") {
            this.$message.success("提交成功");
            this.loadData();
          }
        });
      }
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          ...row,
        };
        api.deleteData(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {
      this.$refs.crud.switchModalView(true, "ADD");
    },
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...row,
        businessTypeIds: row.businessType?.split(",") || [],
      });
    },
    // 查看告警详情
    handleViewDetail(row) {
      this.$router.push({
        path: "/rule-engine/resultSearch/detail",
        query: {
          id: row.alarmCode,
        },
      });
    },

    // 规则名称点击跳转
    handleRuleNameClick(row) {
      // 参考规则引擎主页面的跳转逻辑
      localStorage.setItem("isDebug", "false");
      this.$router.push({
        name: "rule-engine-details",
        query: {
          chainId: row.chainId,
        },
      });
    },

    // 获取风险等级样式类
    getRiskLevelClass(riskLevel) {
      const riskLevelMap = {
        1: "risk-high", // 高风险 - 红色
        2: "risk-medium", // 中风险 - 黄色
        3: "risk-low", // 低风险 - 绿色
      };
      return riskLevelMap[riskLevel] || "";
    },

    // 获取风险等级名称
    getRiskLevelName(riskLevel) {
      const option = this.riskLevelOptions.find(
        (item) => item.dictValue == riskLevel
      );
      return option ? option.dictLabel : riskLevel;
    },

    // 获取状态标签类型
    getStatusTagType(alarmStatus) {
      const statusMap = {
        "0": "danger", // 未解除 - 红色
        "1": "warning", // 处理中 - 橙色
        "2": "success", // 已解除 - 绿色
      };
      return statusMap[alarmStatus] || "info";
    },

    // 获取状态名称
    getStatusName(alarmStatus) {
      const option = this.statusOptions.find(
        (item) => item.dictValue == alarmStatus
      );
      return option ? option.dictLabel : alarmStatus;
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}

// 风险等级样式
.risk-high {
  color: #f56c6c; // 高风险 - 红色
  font-weight: 500;
}

.risk-medium {
  color: #e6a23c; // 中风险 - 黄色
  font-weight: 500;
}

.risk-low {
  color: #67c23a; // 低风险 - 绿色
  font-weight: 500;
}
</style>
