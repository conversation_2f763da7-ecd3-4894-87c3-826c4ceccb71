<!-- 处理记录Tab -->
<template>
  <div class="process-record-tab">
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>处理记录</span>
      </div>

      <div v-loading="loading" class="content-area">
        <Timeline
          :list="processRecords"
          operateTypeTitle="operateType"
          operatorNameTitle="operatorName"
          createTimeTitle="createTime"
          operateDetailTitle="operateDetail"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import Timeline from "@/components/Timeline/index.vue";
import api from "@/api/ruleEngine/resultSearch.js";

export default {
  name: "ProcessRecordTab",
  components: {
    Timeline,
  },
  props: {
    alarmCode: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      processRecords: [],
    };
  },
  created() {
    this.loadProcessRecords();
  },
  methods: {
    // 加载处理记录数据
    async loadProcessRecords() {
      this.loading = true;
      try {
        // 调用API获取处理记录数据
        // const res = await api.getProcessRecords({ alarmCode: this.alarmCode });
        // this.processRecords = res.data;

        // 暂时使用模拟数据
        this.processRecords = [
          {
            operateType: "操作解除",
            operatorName: "乐东",
            createTime: "2020-05-06 14:32",
            operateDetail: "解除原因：【已解除】",
          },
          {
            operateType: "操作解除",
            operatorName: "乐东",
            createTime: "2020-05-06 14:32",
            operateDetail: "解除原因：【处理中】",
          },
          {
            operateType: "系统发现异常",
            operatorName: "乐东",
            createTime: "2020-05-06 14:32",
            operateDetail: "",
          },
        ];

        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        this.loading = false;
        this.$message.error("加载处理记录失败");
        console.error(error);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.process-record-tab {
  padding: 20px;

  .card-title-wrap {
    display: flex;
    align-items: center;

    .card-title-line {
      width: 4px;
      height: 16px;
      background: #00b099;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .content-area {
    min-height: 300px;
  }
}
</style>
