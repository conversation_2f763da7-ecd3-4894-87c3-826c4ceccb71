<!-- 规则逻辑Tab -->
<template>
  <div class="rule-logic-tab">
    <!-- 规则表达式卡片 -->
    <el-card class="logic-card">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>规则表达式</span>
      </div>

      <div v-loading="expressionLoading" class="expression-content">
        <div v-if="expressionData" class="expression-code">
          {{ formatExpression(expressionData) }}
        </div>
        <div v-else class="no-data">
          <el-empty description="暂无表达式数据" />
        </div>
      </div>
    </el-card>

    <!-- 条件分解卡片 -->
    <!-- <el-card class="logic-card">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>条件分解</span>
      </div>

      <div class="condition-content">
        <p class="placeholder-text">条件分解内容将在后续开发中实现</p>
        <p class="placeholder-text">此处将展示规则条件的详细分解和逻辑判断</p>
      </div>
    </el-card> -->

    <!-- 动作记录卡片 -->
    <el-card class="logic-card">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>动作记录</span>
      </div>

      <div class="action-content">
        <div class="action-item">
          <label class="action-label">已执行：</label>
          <div class="action-value">
            <div v-if="executionRecords && executionRecords.length > 0">
              <div
                v-for="(record, index) in executionRecords"
                :key="index"
                class="execution-record"
              >
                {{ record }}
              </div>
            </div>
            <div v-else class="no-data">暂无执行记录</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import api from "@/api/ruleEngine/resultSearch.js";

export default {
  name: "RuleLogicTab",
  props: {
    alarmCode: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      expressionLoading: false,
      expressionData: "",
      executionRecords: [], // 执行记录数组
    };
  },
  created() {
    this.loadRuleLogicData();
  },
  methods: {
    // 加载规则逻辑数据
    async loadRuleLogicData() {
      this.expressionLoading = true;
      try {
        // 调用详情接口获取执行记录
        const res = await api.getDetail({ alarmCode: this.alarmCode });

        // 获取规则表达式数据
        this.expressionData = res.data.elData || "";

        // 获取执行记录数组
        this.executionRecords = res.data.executionRecords || [];

        setTimeout(() => {
          this.expressionLoading = false;
        }, 500);
      } catch (error) {
        this.expressionLoading = false;
        this.$message.error("加载规则逻辑数据失败");
        console.error(error);

        // 使用模拟数据作为fallback
        this.expressionData =
          "IF(temperature > 80 AND status == 'active') THEN SWITCH(level) CASE 'high': sendAlert() CASE 'medium': logWarning() DEFAULT: monitor()";
        this.executionRecords = [
          "发送告警通知",
          "记录日志",
          "更新状态",
          "触发下游节点",
        ];
      }
    },

    // 格式化表达式（参考ExpressionPreview组件）
    formatExpression(expression) {
      if (!expression) return "";

      // 定义需要处理换行的关键词
      const keywords = [
        "IF",
        "SWITCH",
        "FOR",
        "WHILE",
        "ITERATOR",
        "TIMEOUT",
        "PAR",
        "THEN",
        "CASE",
        "DEFAULT",
      ];

      // 1. 在括号后和关键词后添加换行
      let formatted = expression;
      keywords.forEach((keyword) => {
        formatted = formatted.replace(
          new RegExp(`${keyword}\\(`, "g"),
          `${keyword}(\n`
        );
        formatted = formatted.replace(
          new RegExp(`\\s${keyword}\\s`, "g"),
          `\n${keyword} `
        );
      });
      formatted = formatted.replace(/\)/g, ")\n");

      // 2. 处理缩进
      const lines = formatted.split("\n");
      let indentLevel = 0;
      const indentSize = 2;
      formatted = lines
        .map((line) => {
          const trimmedLine = line.trim();

          // 减少缩进的情况
          if (trimmedLine.startsWith(")")) {
            indentLevel = Math.max(0, indentLevel - 1);
          }

          const indent = " ".repeat(indentLevel * indentSize);
          const formattedLine = indent + trimmedLine;

          // 增加缩进的情况
          if (
            (keywords.some((keyword) => trimmedLine.startsWith(keyword)) &&
              trimmedLine.includes("(")) ||
            (trimmedLine.includes("(") && !trimmedLine.includes(")"))
          ) {
            indentLevel++;
          }

          return formattedLine;
        })
        .join("\n");

      // 3. 移除多余的空行
      formatted = formatted
        .split("\n")
        .filter((line) => line.trim())
        .join("\n");

      return formatted;
    },
  },
};
</script>

<style lang="less" scoped>
.rule-logic-tab {
  padding: 20px;

  .logic-card {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .card-title-wrap {
    display: flex;
    align-items: center;

    .card-title-line {
      width: 4px;
      height: 16px;
      background: #00b099;
      margin-right: 8px;
    }

    span {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .expression-content {
    min-height: 200px;

    .expression-code {
      background-color: #fafafa;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      font-family: "Monaco", "Menlo", "Ubuntu Mono", "Consolas",
        "source-code-pro", monospace;
      font-size: 14px;
      line-height: 1.6;
      padding: 16px;
      min-height: 180px;
      margin: 0;
      overflow: auto;
      white-space: pre;
      word-wrap: normal;
      transition: all 0.3s ease;
      cursor: text;

      &:hover {
        border-color: #00b099;
        box-shadow: 0 0 0 2px rgba(0, 176, 153, 0.1);
      }
    }

    .no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 180px;
    }
  }

  .condition-content {
    min-height: 120px;
    padding: 20px;
    background-color: #fafafa;
    border-radius: 6px;

    .placeholder-text {
      color: #999;
      font-size: 14px;
      line-height: 1.6;
      margin: 10px 0;
    }
  }

  .action-content {
    min-height: 120px;
    padding: 20px;

    .action-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;

      .action-label {
        min-width: 120px;
        font-weight: 500;
        color: #333;
        line-height: 1.6;
      }

      .action-value {
        flex: 1;

        .execution-record {
          padding: 8px 12px;
          margin-bottom: 8px;
          background-color: #f5f7fa;
          border-left: 3px solid #00b099;
          border-radius: 4px;
          font-size: 14px;
          line-height: 1.6;
          color: #333;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .no-data {
          color: #999;
          font-style: italic;
        }
      }
    }
  }
}
</style>
