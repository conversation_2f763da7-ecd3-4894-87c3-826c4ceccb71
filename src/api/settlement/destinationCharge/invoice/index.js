import request from "@/utils/request";

/**
 * 目标收费发票管理API接口
 * 提供发票信息的增删改查、上传解析功能
 */
export default {
  /**
   * 分页查询发票信息
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {string} [data.invoiceId] - 发票号码
   * @param {string} [data.accountNumber] - 户号
   * @param {string} [data.billingStartDate] - 账单周期起始日期
   * @param {string} [data.billingEndDate] - 账单周期结束日期
   * @param {string} [data.stationName] - 站点名称
   * @returns {Promise<Object>} 返回分页查询结果
   */
  queryList(data) {
    return request({
      url: "/st/invoice/queryList",
      method: "post",
      data: data,
    });
  },

  /**
   * 导出发票信息Excel
   * @param {Object} data - 导出参数
   * @returns {Promise<Object>} 返回导出结果
   */
  exportData(data) {
    return request({
      url: "/st/invoice/export",
      method: "post",
      data: data,
    });
  },

  /**
   * 删除发票信息
   * @param {Object} params - 删除参数
   * @param {number} params.id - 要删除的发票信息ID（必填）
   * @param {string} params.invoiceId - 发票号码
   * @returns {Promise<Object>} 返回删除结果
   */
  remove(params) {
    return request({
      url: "/st/invoice/deleteInfo",
      method: "get",
      params: params,
    });
  },

  /**
   * 编辑发票信息
   * @param {Object} data - 发票信息数据
   * @returns {Promise<Object>} 返回保存结果
   */
  update(data) {
    return request({
      url: "/st/invoice/editInfo",
      method: "post",
      data: data,
    });
  },

  /**
   * 发票文件解析
   * @param {Object} data - 解析参数
   * @param {Array} data.fileList - 要解析的文件信息列表（必填）
   * @param {string} data.fileList[].storePath - 文件存储路径
   * @param {string} data.fileList[].docName - 文件名称
   * @returns {Promise<Object>} 返回解析结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Object} returns.data - 解析结果数据
   * @returns {number} returns.data.successCount - 解析成功条数
   * @returns {number} returns.data.failCount - 解析失败条数
   * @returns {Array} returns.data.failList - 解析失败的文件列表
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 解析发票文件
   * const result = await invoiceApi.parseInvoices({
   *   fileList: [
   *     { storePath: '/path/to/file1.pdf', docName: 'invoice1.pdf' },
   *     { storePath: '/path/to/file2.png', docName: 'invoice2.png' }
   *   ]
   * });
   */
  /**
   * 批量导入发票文件
   * @param {FormData} data - 包含文件的FormData对象
   * @returns {Promise<Object>} 返回导入结果
   */
  batchImport(data) {
    return request({
      url: "/st/invoice/recognize",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  /**
   * 查询站点列表（用于站点选择器）
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {string} [data.stationCode] - 站点编号（模糊查询）
   * @param {string} [data.stationName] - 站点名称（模糊查询）
   * @returns {Promise<Object>} 返回站点列表查询结果
   */
  queryStationList(data) {
    return request({
      url: "/destinationCharge/invoice/queryStationList",
      method: "post",
      data: data,
    });
  },

  /**
   * 获取站点名称列表
   * @param {Object} params - 查询参数
   * @param {string} [params.name] - 站点名称（模糊查询）
   * @returns {Promise<Object>} 返回站点名称列表
   */
  getStationNameList(params) {
    return request({
      url: "/st/invoice/stationName",
      method: "get",
      params: params,
    });
  },

  /**
   * 获取发票类型列表
   * @param {Object} params - 查询参数
   * @param {string} [params.name] - 发票类型（模糊查询）
   * @returns {Promise<Object>} 返回发票类型列表
   */
  getInvoiceTypeList(params) {
    return request({
      url: "/st/invoice/invoiceType",
      method: "get",
      params: params,
    });
  },

  /**
   * 分页查询发票解析记录
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {number} [data.invoiceRecordId] - 发票记录ID（用于查询特定发票的解析记录）
   * @returns {Promise<Object>} 返回分页查询结果
   */
  queryInvoiceRecordList(data) {
    return request({
      url: "/st/invoice/record/queryList",
      method: "post",
      data: data,
    });
  },
};
