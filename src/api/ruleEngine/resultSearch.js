import request from "@/utils/request";
//结果查询
export default {
  // 分页查询告警列表 - 根据YApi接口文档
  getTableData(data) {
    return request({
      url: "/chain/alarm/queryList",
      method: "post",
      data: data,
    });
  },

  // 解除预警 - 根据YApi接口文档
  relieve(data) {
    return request({
      url: "/chain/alarm/relieve",
      method: "post",
      data: data,
    });
  },

  // 获取告警详情 - 根据YApi接口文档
  getDetail(data) {
    return request({
      url: "/chain/alarm/detail",
      method: "post",
      data: data,
    });
  },

  // 获取执行日志 - 根据YApi接口文档
  getExecutionLog(data) {
    return request({
      url: "/chain/alarm/executionLog",
      method: "post",
      data: data,
    });
  },

  // 获取同规则预警趋势 - 根据YApi接口文档
  getTendency(data) {
    return request({
      url: "/chain/alarm/tendency",
      method: "post",
      data: data,
    });
  },

  // 获取符合解除条件的条数 - 根据YApi接口文档
  getDismissibleCount(data) {
    return request({
      url: "/chain/alarm/dismissibleCount",
      method: "post",
      data: data,
    });
  },

  // 删除
  deleteData(query) {
    return request({
      url: "/aging/config/delete",
      method: "post",
      data: query,
    });
  },
  // 导出
  export(query) {
    return request({
      url: "/checkItemDef/update",
      method: "post",
      data: query,
    });
  },
  //查询日志
  queryLog(data) {
    return request({
      url: "/ledger/urgencyDegree/log/list",
      method: "get",
      params: data,
    });
  },
  //保存/新增 - 保留原有接口用于解除功能
  update(query) {
    return request({
      url: "/ledger/urgencyDegree/save",
      method: "post",
      data: query,
    });
  },
  //状态切换
  changeStatus(query) {
    return request({
      url: "/ledger/urgencyDegree/changeStatus",
      method: "post",
      data: query,
    });
  },

  // 告警详情相关接口（保留用于详情页面）
  // 获取告警详情
  getAlarmDetail(query) {
    return request({
      url: "/ruleEngine/alarm/detail",
      method: "get",
      params: query,
    });
  },

  // 获取预警趋势数据
  getTrendData(query) {
    return request({
      url: "/ruleEngine/alarm/trend",
      method: "post",
      data: query,
    });
  },

  // 获取规则表达式数据
  getRuleExpression(query) {
    return request({
      url: "/ruleEngine/alarm/ruleExpression",
      method: "get",
      params: query,
    });
  },

  // 获取规则逻辑数据
  getRuleLogicData(query) {
    return request({
      url: "/ruleEngine/alarm/ruleLogic",
      method: "get",
      params: query,
    });
  },

  // 获取上下文数据
  getContextData(query) {
    return request({
      url: "/ruleEngine/alarm/contextData",
      method: "get",
      params: query,
    });
  },

  // 获取处理记录
  getProcessRecords(query) {
    return request({
      url: "/ruleEngine/alarm/processRecords",
      method: "get",
      params: query,
    });
  },

  // 获取执行日志
  getExecutionLogs(query) {
    return request({
      url: "/ruleEngine/alarm/executionLogs",
      method: "get",
      params: query,
    });
  },

  // 解除告警
  releaseAlarm(data) {
    return request({
      url: "/ruleEngine/alarm/release",
      method: "post",
      data: data,
    });
  },

  // 获取解除进度选项
  getReleaseStatusOptions() {
    return request({
      url: "/ruleEngine/alarm/releaseStatusOptions",
      method: "get",
    });
  },
};
